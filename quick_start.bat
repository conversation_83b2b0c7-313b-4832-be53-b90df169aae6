@echo off
title Quick Start Monitor

echo ========================================
echo Quick Start Monitor (Skip All Checks)
echo ========================================
echo.

echo 🔍 检查虚拟环境...
if not exist ".venv\Scripts\python.exe" (
    echo ERROR: Virtual environment not found
    echo 当前目录: %CD%
    echo 查找的路径: %CD%\.venv\Scripts\python.exe
    pause
    exit /b 1
)

echo ✅ 虚拟环境找到: %CD%\.venv\Scripts\python.exe
echo.

echo 🔍 测试虚拟环境Python...
.venv\Scripts\python.exe --version
if %ERRORLEVEL% neq 0 (
    echo ERROR: Virtual environment Python is not working
    pause
    exit /b 1
)

echo.
echo 🚀 Starting monitor without dependency checks...
echo.

:: Set environment variable to skip checks
set SKIP_DEPENDENCY_CHECK=1

.venv\Scripts\python.exe auto_restart.py

echo.
echo Monitor stopped
pause
