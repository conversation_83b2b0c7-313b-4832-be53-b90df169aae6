#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动重启监控器配置文件
可以根据需要调整这些参数
"""

# 监控配置
MONITOR_CONFIG = {
    # 最大重启次数（防止无限重启）
    "max_restarts": 10,
    
    # 重启延迟时间（秒）
    "restart_delay": 5,
    
    # 进程检查间隔（秒）
    "check_interval": 0.1,
    
    # 是否显示主程序输出
    "show_main_output": True,
    
    # 日志级别 (DEBUG, INFO, WARNING, ERROR)
    "log_level": "DEBUG",
    
    # 是否保存主程序输出到单独文件
    "save_main_output": True,
}

# 路径配置
PATH_CONFIG = {
    # 主脚本名称
    "main_script": "main.py",
    
    # 虚拟环境Python路径（相对于项目根目录）
    "venv_python": ".venv/Scripts/python.exe",
    
    # 日志目录
    "log_dir": "logs",
    
    # 停止标志文件名
    "stop_flag": "stop_monitor.flag",
}

# 高级配置
ADVANCED_CONFIG = {
    # 是否在重启前清理临时文件
    "cleanup_temp_files": True,

    # 临时文件目录列表
    "temp_dirs": ["temp_images", "__pycache__"],

    # 是否在启动前检查依赖（设为False可跳过依赖检查）
    "check_dependencies": False,  # 改为False，避免依赖检查问题

    # 必需的依赖包列表
    "required_packages": ["playwright", "requests", "Pillow"],
    
    # 是否发送通知（需要额外配置）
    "enable_notifications": False,
    
    # 通知配置
    "notification_config": {
        "webhook_url": "",  # 可以配置钉钉、企业微信等webhook
        "email_config": {
            "smtp_server": "",
            "smtp_port": 587,
            "username": "",
            "password": "",
            "to_email": "",
        }
    }
}

def get_config():
    """获取完整配置"""
    return {
        "monitor": MONITOR_CONFIG,
        "path": PATH_CONFIG,
        "advanced": ADVANCED_CONFIG,
    }

def update_config(**kwargs):
    """更新配置"""
    for section, updates in kwargs.items():
        if section == "monitor":
            MONITOR_CONFIG.update(updates)
        elif section == "path":
            PATH_CONFIG.update(updates)
        elif section == "advanced":
            ADVANCED_CONFIG.update(updates)

# 使用示例：
# from monitor_config import get_config, update_config
# 
# # 获取配置
# config = get_config()
# 
# # 更新配置
# update_config(monitor={"max_restarts": 20, "restart_delay": 10})
