#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR图片识别处理模块
支持多种OCR引擎，专门优化数学公式识别
"""

import requests
import os
import time
import re
from typing import List, Optional, Tuple
from PIL import Image
from io import BytesIO

class OCRHandler:
    """OCR处理器，支持多种识别引擎"""
    
    def __init__(self, debug_mode: bool = True):
        self.debug_mode = debug_mode
        self.temp_dir = "temp_images"
        self.ensure_temp_dir()
        
        # 尝试初始化可用的OCR引擎
        self.available_engines = self.check_available_engines()
        print(f"[OCR] 可用的OCR引擎: {self.available_engines}")
    
    def ensure_temp_dir(self):
        """确保临时目录存在"""
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)
    
    def check_available_engines(self) -> List[str]:
        """检查可用的OCR引擎"""
        engines = []
        
        # 检查PaddleOCR
        try:
            import paddleocr
            engines.append("paddleocr")
        except ImportError:
            pass
        
        # 检查Tesseract
        try:
            import pytesseract
            engines.append("tesseract")
        except ImportError:
            pass
        
        # 总是可用的基础引擎
        engines.append("basic")
        
        return engines
    
    def download_image(self, img_url: str) -> Optional[str]:
        """下载图片到本地"""
        try:
            print(f"[下载] 正在下载图片: {img_url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://zydz-menhu.ouchn.edu.cn/',
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8'
            }
            
            response = requests.get(img_url, headers=headers, timeout=15)
            response.raise_for_status()
            
            # 生成唯一的文件名
            timestamp = int(time.time() * 1000)
            img_filename = os.path.join(self.temp_dir, f"img_{timestamp}.png")
            
            # 保存图片
            with open(img_filename, 'wb') as f:
                f.write(response.content)
            
            print(f"[保存] 图片已保存: {img_filename}")
            return img_filename

        except Exception as e:
            print(f"[错误] 图片下载失败: {e}")
            return None
    
    def ocr_with_paddleocr(self, img_path: str) -> str:
        """使用PaddleOCR识别图片"""
        try:
            import paddleocr
            
            # 初始化PaddleOCR
            ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang='ch', show_log=False)
            
            # 进行OCR识别
            result = ocr.ocr(img_path, cls=True)
            
            # 提取文本
            texts = []
            if result and result[0]:
                for line in result[0]:
                    if line and len(line) > 1:
                        text = line[1][0]  # 获取识别的文本
                        confidence = line[1][1]  # 获取置信度
                        if confidence > 0.5:  # 只保留置信度较高的结果
                            texts.append(text)
            
            ocr_result = " ".join(texts)
            print(f"[PaddleOCR] 识别结果: {ocr_result}")
            return ocr_result

        except Exception as e:
            print(f"[错误] PaddleOCR识别失败: {e}")
            return ""
    
    def ocr_with_tesseract(self, img_path: str) -> str:
        """使用Tesseract识别图片"""
        try:
            import pytesseract
            from PIL import Image
            
            # 打开图片
            image = Image.open(img_path)
            
            # 进行OCR识别，使用中文+英文
            custom_config = r'--oem 3 --psm 6 -l chi_sim+eng'
            text = pytesseract.image_to_string(image, config=custom_config)
            
            # 清理结果
            text = text.strip().replace('\n', ' ')
            print(f"[Tesseract] 识别结果: {text}")
            return text

        except Exception as e:
            print(f"[错误] Tesseract识别失败: {e}")
            return ""
    
    def ocr_basic_fallback(self, img_url: str) -> str:
        """基础回退方案，从URL或文件名推测内容"""
        try:
            # 尝试从URL中提取有用信息
            filename = os.path.basename(img_url)
            
            # 如果文件名包含数字，可能是题目编号
            numbers = re.findall(r'\d+', filename)
            if numbers:
                return f"[数学公式_{numbers[0]}]"
            
            return "[数学公式]"
            
        except:
            return "[图片内容]"
    
    def recognize_image(self, img_url: str) -> str:
        """识别图片内容，自动选择最佳引擎"""
        try:
            # 下载图片
            img_path = self.download_image(img_url)
            if not img_path:
                return self.ocr_basic_fallback(img_url)
            
            ocr_result = ""
            
            # 按优先级尝试不同的OCR引擎
            if "paddleocr" in self.available_engines:
                ocr_result = self.ocr_with_paddleocr(img_path)
            
            if not ocr_result and "tesseract" in self.available_engines:
                ocr_result = self.ocr_with_tesseract(img_path)
            
            # 如果所有OCR都失败，使用基础回退
            if not ocr_result:
                ocr_result = self.ocr_basic_fallback(img_url)
            
            # 清理临时文件
            try:
                os.remove(img_path)
            except:
                pass
            
            return ocr_result
            
        except Exception as e:
            print(f"[错误] 图片识别失败: {e}")
            return self.ocr_basic_fallback(img_url)

    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            if os.path.exists(self.temp_dir):
                for file in os.listdir(self.temp_dir):
                    file_path = os.path.join(self.temp_dir, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                print("[清理] 临时文件已清理")
        except Exception as e:
            print(f"[警告] 清理临时文件失败: {e}")

# 全局OCR处理器实例
ocr_handler = OCRHandler()

def 识别图片内容(img_url: str) -> str:
    """便捷的图片识别函数"""
    return ocr_handler.recognize_image(img_url)

def 清理临时文件():
    """清理临时文件的便捷函数"""
    ocr_handler.cleanup_temp_files()
