#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径诊断脚本 - 帮助调试云服务器上的路径问题
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    print("🔍 路径诊断工具")
    print("="*50)
    
    # 当前脚本目录
    script_dir = Path(__file__).parent
    print(f"📁 脚本目录: {script_dir}")
    print(f"📁 脚本目录绝对路径: {script_dir.absolute()}")
    
    # 虚拟环境路径
    venv_python = script_dir / ".venv" / "Scripts" / "python.exe"
    print(f"🐍 虚拟环境Python路径: {venv_python}")
    print(f"🐍 虚拟环境Python绝对路径: {venv_python.absolute()}")
    print(f"✅ 虚拟环境Python存在: {venv_python.exists()}")
    
    # 当前Python路径
    print(f"🐍 当前Python路径: {sys.executable}")
    
    # 环境变量
    print(f"🌍 PATH环境变量:")
    for path in os.environ.get('PATH', '').split(os.pathsep):
        if 'python' in path.lower() or 'miniconda' in path.lower() or 'anaconda' in path.lower():
            print(f"   {path}")
    
    # 测试虚拟环境Python
    if venv_python.exists():
        print("\n🧪 测试虚拟环境Python:")
        try:
            cmd = [str(venv_python), "--version"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            print(f"   版本: {result.stdout.strip()}")
            print(f"   返回码: {result.returncode}")
            if result.stderr:
                print(f"   错误: {result.stderr}")
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    # 测试导入
    print("\n📦 测试依赖导入:")
    test_packages = ["requests", "PIL", "playwright"]
    
    for package in test_packages:
        print(f"\n测试包: {package}")
        
        # 使用当前Python测试
        try:
            __import__(package)
            print(f"   ✅ 当前Python可导入")
        except ImportError:
            print(f"   ❌ 当前Python无法导入")
        
        # 使用虚拟环境Python测试
        if venv_python.exists():
            try:
                cmd = [str(venv_python), "-c", f"import {package}; print('OK')"]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"   ✅ 虚拟环境Python可导入")
                else:
                    print(f"   ❌ 虚拟环境Python无法导入")
                    print(f"      错误: {result.stderr}")
            except Exception as e:
                print(f"   ❌ 虚拟环境测试失败: {e}")

if __name__ == "__main__":
    main()
