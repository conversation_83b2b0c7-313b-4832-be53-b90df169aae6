@echo off
title Smart Start Monitor

echo ========================================
echo Smart Start Monitor (Auto-Fix)
echo ========================================
echo.

echo 🔍 Step 1: Checking virtual environment...
echo.

if not exist ".venv\Scripts\python.exe" (
    echo ❌ Virtual environment not found
    echo 🆕 Creating new virtual environment...
    python -m venv .venv
    if %ERRORLEVEL% neq 0 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created
    goto install_deps
)

echo ✅ Virtual environment found
echo.

echo 🧪 Step 2: Testing virtual environment...
.venv\Scripts\python.exe --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Virtual environment is broken
    echo 🔧 Running fix script...
    python fix_venv.py
    if %ERRORLEVEL% neq 0 (
        echo ERROR: Failed to fix virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment fixed
) else (
    echo ✅ Virtual environment is working
)

:install_deps
echo.
echo 📦 Step 3: Checking dependencies...
if exist "requirements.txt" (
    echo Installing/updating dependencies...
    .venv\Scripts\pip.exe install -r requirements.txt
    if %ERRORLEVEL% neq 0 (
        echo ⚠️ Some dependencies failed to install, but continuing...
    )
) else (
    echo ⚠️ No requirements.txt found
)

echo.
echo 🚀 Step 4: Starting monitor...
echo.

:: Set environment variable to skip checks
set SKIP_DEPENDENCY_CHECK=1

.venv\Scripts\python.exe auto_restart.py

echo.
echo Monitor stopped
pause
