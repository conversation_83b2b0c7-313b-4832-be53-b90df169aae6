#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟环境修复脚本
修复虚拟环境中的Python路径配置
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def main():
    print("🔧 虚拟环境修复工具")
    print("="*50)
    
    script_dir = Path(__file__).parent
    venv_dir = script_dir / ".venv"
    
    print(f"📁 项目目录: {script_dir}")
    print(f"📁 虚拟环境目录: {venv_dir}")
    
    if not venv_dir.exists():
        print("❌ 虚拟环境不存在，需要创建新的虚拟环境")
        create_new_venv(script_dir)
        return
    
    # 检查 pyvenv.cfg
    pyvenv_cfg = venv_dir / "pyvenv.cfg"
    if pyvenv_cfg.exists():
        print(f"\n📄 检查 pyvenv.cfg:")
        with open(pyvenv_cfg, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
        
        # 检查是否包含错误的路径
        if "G:\\APP\\Miniconda" in content:
            print("❌ 发现错误的路径配置！")
            fix_pyvenv_cfg(pyvenv_cfg)
        else:
            print("✅ pyvenv.cfg 看起来正常")
    
    # 检查 python.exe
    python_exe = venv_dir / "Scripts" / "python.exe"
    if python_exe.exists():
        print(f"\n🐍 测试修复后的Python:")
        try:
            result = subprocess.run([str(python_exe), "--version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ Python版本: {result.stdout.strip()}")
            else:
                print(f"❌ Python测试失败: {result.stderr}")
                print("建议重新创建虚拟环境")
                recreate_venv(script_dir)
        except Exception as e:
            print(f"❌ Python测试异常: {e}")
            print("建议重新创建虚拟环境")
            recreate_venv(script_dir)

def fix_pyvenv_cfg(pyvenv_cfg_path):
    """修复 pyvenv.cfg 文件"""
    print("🔧 修复 pyvenv.cfg...")
    
    # 获取当前系统的Python路径
    current_python = sys.executable
    python_dir = Path(current_python).parent
    
    print(f"当前Python路径: {current_python}")
    print(f"Python目录: {python_dir}")
    
    # 读取原文件
    with open(pyvenv_cfg_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 修复路径
    new_lines = []
    for line in lines:
        if line.startswith('home = '):
            new_line = f"home = {python_dir}\n"
            print(f"修复: {line.strip()} -> {new_line.strip()}")
            new_lines.append(new_line)
        elif line.startswith('executable = '):
            new_line = f"executable = {current_python}\n"
            print(f"修复: {line.strip()} -> {new_line.strip()}")
            new_lines.append(new_line)
        else:
            new_lines.append(line)
    
    # 写回文件
    with open(pyvenv_cfg_path, 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    print("✅ pyvenv.cfg 已修复")

def create_new_venv(project_dir):
    """创建新的虚拟环境"""
    print("🆕 创建新的虚拟环境...")
    
    venv_dir = project_dir / ".venv"
    
    try:
        # 创建虚拟环境
        subprocess.run([sys.executable, "-m", "venv", str(venv_dir)], 
                      check=True, cwd=str(project_dir))
        print("✅ 虚拟环境创建成功")
        
        # 安装依赖
        install_requirements(project_dir)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 创建虚拟环境失败: {e}")

def recreate_venv(project_dir):
    """重新创建虚拟环境"""
    print("🔄 重新创建虚拟环境...")
    
    venv_dir = project_dir / ".venv"
    
    # 备份 requirements.txt
    requirements_file = project_dir / "requirements.txt"
    if not requirements_file.exists():
        print("⚠️ 未找到 requirements.txt，请手动安装依赖")
    
    # 删除旧的虚拟环境
    if venv_dir.exists():
        print("🗑️ 删除旧的虚拟环境...")
        shutil.rmtree(venv_dir)
    
    # 创建新的虚拟环境
    create_new_venv(project_dir)

def install_requirements(project_dir):
    """安装依赖包"""
    requirements_file = project_dir / "requirements.txt"
    if not requirements_file.exists():
        print("⚠️ 未找到 requirements.txt")
        return
    
    venv_python = project_dir / ".venv" / "Scripts" / "python.exe"
    venv_pip = project_dir / ".venv" / "Scripts" / "pip.exe"
    
    print("📦 安装依赖包...")
    try:
        # 升级pip
        subprocess.run([str(venv_python), "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, cwd=str(project_dir))
        
        # 安装依赖
        subprocess.run([str(venv_pip), "install", "-r", "requirements.txt"], 
                      check=True, cwd=str(project_dir))
        print("✅ 依赖包安装成功")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装依赖失败: {e}")

if __name__ == "__main__":
    main()
