@echo off
title Safe Start Monitor

echo ========================================
echo Safe Start Monitor (With Diagnostics)
echo ========================================
echo.

echo 🔍 Step 1: Running diagnostics...
echo.

if not exist ".venv\Scripts\python.exe" (
    echo ERROR: Virtual environment not found
    echo 当前目录: %CD%
    echo 查找的路径: %CD%\.venv\Scripts\python.exe
    pause
    exit /b 1
)

echo ✅ Virtual environment found
echo.

echo 🧪 Step 2: Testing virtual environment...
.venv\Scripts\python.exe debug_paths.py
if %ERRORLEVEL% neq 0 (
    echo ERROR: Diagnostics failed
    pause
    exit /b 1
)

echo.
echo 🤔 Do you want to continue with the monitor? (Y/N)
set /p choice="Enter your choice: "
if /i "%choice%" neq "Y" (
    echo Cancelled by user
    pause
    exit /b 0
)

echo.
echo 🚀 Step 3: Starting monitor...
echo.

:: Set environment variable to skip checks
set SKIP_DEPENDENCY_CHECK=1

.venv\Scripts\python.exe auto_restart.py

echo.
echo Monitor stopped
pause
